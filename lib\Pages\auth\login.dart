import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:hue/Pages/home/<USER>';
import 'package:hue/Pages/auth/multi_step_signup.dart';
import 'package:hue/core/services/error_handler.dart';
import 'package:hue/core/services/navigation_service.dart';
import 'package:hue/core/utils/constants.dart';
import 'package:hue/core/widgets/app_background.dart';

class Login extends StatefulWidget {
  const Login({super.key});

  @override
  State<Login> createState() => _LoginState();
}

class _LoginState extends State<Login> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _rememberMe = false;

  final SupabaseClient _supabase = Supabase.instance.client;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  // تسجيل الدخول
  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من الاتصال بالإنترنت
    if (!await _checkInternetConnection()) {
      _showErrorMessage('لا يوجد اتصال بالإنترنت');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final email = _emailController.text.trim();
      final password = _passwordController.text;

      // تسجيل الدخول باستخدام Supabase
      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // حفظ بيانات المستخدم إذا كان يريد تذكره
        if (_rememberMe) {
          await _saveUserCredentials(email);
        }

        _showSuccessMessage('تم تسجيل الدخول بنجاح');

        // تأخير قصير لإظهار رسالة النجاح
        await Future.delayed(const Duration(milliseconds: 500));

        // الانتقال إلى الصفحة الرئيسية
        if (mounted) {
          NavigationService.navigateAndRemoveUntil(const HomePage());
        }
      } else {
        _showErrorMessage('فشل في تسجيل الدخول - بيانات غير صحيحة');
      }
    } catch (e) {
      final errorMessage = ErrorHandler.handleSupabaseError(e);
      _showErrorMessage(errorMessage);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // التحقق من الاتصال بالإنترنت
  Future<bool> _checkInternetConnection() async {
    try {
      // محاولة الاتصال بـ Supabase
      await _supabase.from('profiles').select('id').limit(1);
      return true;
    } catch (e) {
      return false;
    }
  }

  // حفظ بيانات المستخدم للتذكر
  Future<void> _saveUserCredentials(String email) async {
    try {
      // يمكن استخدام SharedPreferences أو SecureStorage هنا
      // للبساطة، سنحفظ فقط البريد الإلكتروني
      // في تطبيق حقيقي، لا يجب حفظ كلمة المرور
    } catch (e) {
      // تجاهل الأخطاء في حفظ البيانات
    }
  }

  // إعادة تعيين كلمة المرور
  Future<void> _resetPassword() async {
    final email = _emailController.text.trim();

    if (email.isEmpty) {
      _showErrorMessage('يرجى إدخال البريد الإلكتروني أولاً');
      return;
    }

    if (!GetUtils.isEmail(email)) {
      _showErrorMessage('يرجى إدخال بريد إلكتروني صحيح');
      return;
    }

    if (!email.endsWith(Constants.emailDomain)) {
      _showErrorMessage(Constants.invalidEmailDomain);
      return;
    }

    try {
      await _supabase.auth.resetPasswordForEmail(
        email,
        redirectTo: 'https://your-app.com/reset-password', // يجب تغيير هذا الرابط
      );

      _showSuccessMessage('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني');
    } catch (e) {
      final errorMessage = ErrorHandler.handleSupabaseError(e);
      _showErrorMessage(errorMessage);
    }
  }

  // عرض رسالة خطأ
  void _showErrorMessage(String message) {
    if (mounted) {
      NavigationService.showErrorSnackbar(message);
    }
  }

  // عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    if (mounted) {
      NavigationService.showSuccessSnackbar(message);
    }
  }

  // التحقق من صحة البريد الإلكتروني
  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال البريد الإلكتروني';
    }

    // إزالة المسافات الزائدة
    value = value.trim();

    if (!GetUtils.isEmail(value)) {
      return 'يرجى إدخال بريد إلكتروني صحيح';
    }

    if (!value.endsWith(Constants.emailDomain)) {
      return Constants.invalidEmailDomain;
    }

    return null;
  }

  // التحقق من صحة كلمة المرور
  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال كلمة المرور';
    }

    if (value.length < 8) {
      return Constants.invalidPassword;
    }

    // التحقق من قوة كلمة المرور
    if (!_isPasswordStrong(value)) {
      return 'كلمة المرور يجب أن تحتوي على أحرف وأرقام';
    }

    return null;
  }

  // التحقق من قوة كلمة المرور
  bool _isPasswordStrong(String password) {
    // التحقق من وجود أحرف وأرقام
    final hasLetters = password.contains(RegExp(r'[a-zA-Z]'));
    final hasNumbers = password.contains(RegExp(r'[0-9]'));

    return hasLetters && hasNumbers;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('تسجيل الدخول'),
        centerTitle: true,
      ),
      body: AppBackground(
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                const SizedBox(height: 40),
                
                // شعار التطبيق
                Icon(
                  Icons.school,
                  size: 80,
                  color: Theme.of(context).primaryColor,
                ),
                
                const SizedBox(height: 24),
                
                Text(
                  'مرحباً بك',
                  style: Theme.of(context).textTheme.headlineLarge,
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 8),
                
                Text(
                  'سجل دخولك للمتابعة',
                  style: Theme.of(context).textTheme.bodyLarge,
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 40),
                
                // حقل البريد الإلكتروني
                TextFormField(
                  controller: _emailController,
                  keyboardType: TextInputType.emailAddress,
                  textInputAction: TextInputAction.next,
                  validator: _validateEmail,
                  enabled: !_isLoading,
                  decoration: const InputDecoration(
                    labelText: 'البريد الإلكتروني',
                    hintText: '<EMAIL>',
                    prefixIcon: Icon(Icons.email_outlined),
                    helperText: 'استخدم بريدك الجامعي',
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // حقل كلمة المرور
                TextFormField(
                  controller: _passwordController,
                  obscureText: _obscurePassword,
                  textInputAction: TextInputAction.done,
                  validator: _validatePassword,
                  enabled: !_isLoading,
                  onFieldSubmitted: (_) => _isLoading ? null : _login(),
                  decoration: InputDecoration(
                    labelText: 'كلمة المرور',
                    hintText: 'أدخل كلمة المرور',
                    prefixIcon: const Icon(Icons.lock_outlined),
                    helperText: 'على الأقل 8 أحرف مع أرقام وحروف',
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscurePassword
                            ? Icons.visibility_outlined
                            : Icons.visibility_off_outlined,
                      ),
                      onPressed: _isLoading ? null : () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // خانة تذكرني
                Row(
                  children: [
                    Checkbox(
                      value: _rememberMe,
                      onChanged: (value) {
                        setState(() {
                          _rememberMe = value ?? false;
                        });
                      },
                    ),
                    const Text('تذكرني'),
                  ],
                ),

                const SizedBox(height: 24),

                // زر تسجيل الدخول
                SizedBox(
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _login,
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text('تسجيل الدخول'),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // رابط نسيان كلمة المرور
                TextButton(
                  onPressed: _resetPassword,
                  child: const Text('نسيت كلمة المرور؟'),
                ),
                
                const SizedBox(height: 24),
                
                // خط فاصل
                Row(
                  children: [
                    const Expanded(child: Divider()),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        'أو',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                    const Expanded(child: Divider()),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                // رابط إنشاء حساب جديد
                OutlinedButton(
                  onPressed: () {
                    NavigationService.navigateTo(const MultiStepSignUpPage());
                  },
                  child: const Text('إنشاء حساب جديد'),
                ),
              ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
