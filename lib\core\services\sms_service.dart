import 'dart:math';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:telephony/telephony.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:shared_preferences/shared_preferences.dart';

class SmsService {
  static final Telephony telephony = Telephony.instance;
  
  // Generate a random 6-digit verification code
  static String generateVerificationCode() {
    final random = Random();
    return (100000 + random.nextInt(900000)).toString();
  }
  
  // Save verification code with expiry time
  static Future<void> saveVerificationCode(String phoneNumber, String code) async {
    final prefs = await SharedPreferences.getInstance();
    final expiryTime = DateTime.now().add(const Duration(minutes: 5)).millisecondsSinceEpoch;
    
    await prefs.setString('verification_code_$phoneNumber', code);
    await prefs.setInt('verification_expiry_$phoneNumber', expiryTime);
  }
  
  // Verify the entered code
  static Future<bool> verifyCode(String phoneNumber, String enteredCode) async {
    final prefs = await SharedPreferences.getInstance();
    final savedCode = prefs.getString('verification_code_$phoneNumber');
    final expiryTime = prefs.getInt('verification_expiry_$phoneNumber');
    
    if (savedCode == null || expiryTime == null) {
      return false;
    }
    
    // Check if code has expired
    if (DateTime.now().millisecondsSinceEpoch > expiryTime) {
      // Clean up expired code
      await prefs.remove('verification_code_$phoneNumber');
      await prefs.remove('verification_expiry_$phoneNumber');
      return false;
    }
    
    final isValid = savedCode == enteredCode;
    
    // Clean up if verification is successful
    if (isValid) {
      await prefs.remove('verification_code_$phoneNumber');
      await prefs.remove('verification_expiry_$phoneNumber');
    }
    
    return isValid;
  }
  
  // Send SMS using device's SMS capability
  static Future<bool> sendSMS(String phoneNumber, String message) async {
    try {
      // Request SMS permission
      final permission = await Permission.sms.request();
      if (permission != PermissionStatus.granted) {
        Get.snackbar(
          'خطأ في الصلاحيات',
          'يرجى السماح بإرسال الرسائل النصية',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
      
      // Send SMS
      await telephony.sendSms(
        to: phoneNumber,
        message: message,
      );
      
      return true;
    } catch (e) {
      debugPrint('Error sending SMS: $e');
      return false;
    }
  }
  
  // Send WhatsApp message
  static Future<bool> sendWhatsAppMessage(String phoneNumber, String message) async {
    try {
      // Format phone number for WhatsApp (remove leading 0 and add country code)
      String formattedNumber = phoneNumber;
      if (formattedNumber.startsWith('0')) {
        formattedNumber = '2${formattedNumber.substring(1)}'; // Egypt country code
      }
      
      final whatsappUrl = 'https://wa.me/$formattedNumber?text=${Uri.encodeComponent(message)}';
      final uri = Uri.parse(whatsappUrl);
      
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        return true;
      } else {
        return false;
      }
    } catch (e) {
      debugPrint('Error sending WhatsApp message: $e');
      return false;
    }
  }
  
  // Send verification code via SMS
  static Future<bool> sendVerificationCodeSMS(String phoneNumber) async {
    try {
      final code = generateVerificationCode();
      final message = '''
مرحباً بك في تطبيق HUE! 🎓

رمز التحقق الخاص بك هو: $code

هذا الرمز صالح لمدة 5 دقائق فقط.
لا تشارك هذا الرمز مع أي شخص آخر.

فريق HUE
''';
      
      final success = await sendSMS(phoneNumber, message);
      if (success) {
        await saveVerificationCode(phoneNumber, code);
      }
      
      return success;
    } catch (e) {
      debugPrint('Error sending verification SMS: $e');
      return false;
    }
  }
  
  // Send verification code via WhatsApp
  static Future<bool> sendVerificationCodeWhatsApp(String phoneNumber) async {
    try {
      final code = generateVerificationCode();
      final message = '''
🎓 مرحباً بك في تطبيق HUE!

رمز التحقق الخاص بك هو: *$code*

⏰ هذا الرمز صالح لمدة 5 دقائق فقط
🔒 لا تشارك هذا الرمز مع أي شخص آخر

شكراً لك!
فريق HUE 💙
''';
      
      await saveVerificationCode(phoneNumber, code);
      return await sendWhatsAppMessage(phoneNumber, message);
    } catch (e) {
      debugPrint('Error sending verification WhatsApp: $e');
      return false;
    }
  }
  
  // Show verification method selection dialog
  static Future<bool?> showVerificationMethodDialog(String phoneNumber) async {
    return await Get.dialog<bool>(
      AlertDialog(
        title: const Text('اختر طريقة التحقق'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('سيتم إرسال رمز التحقق إلى الرقم:\n$phoneNumber'),
            const SizedBox(height: 20),
            const Text('اختر الطريقة المفضلة:'),
          ],
        ),
        actions: [
          TextButton.icon(
            onPressed: () async {
              Get.back();
              final success = await sendVerificationCodeSMS(phoneNumber);
              if (success) {
                Get.snackbar(
                  'تم الإرسال',
                  'تم إرسال رمز التحقق عبر الرسائل النصية',
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } else {
                Get.snackbar(
                  'خطأ',
                  'فشل في إرسال الرسالة النصية',
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            icon: const Icon(Icons.sms),
            label: const Text('رسالة نصية'),
          ),
          TextButton.icon(
            onPressed: () async {
              Get.back();
              final success = await sendVerificationCodeWhatsApp(phoneNumber);
              if (success) {
                Get.snackbar(
                  'تم الإرسال',
                  'تم إرسال رمز التحقق عبر واتساب',
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } else {
                Get.snackbar(
                  'خطأ',
                  'فشل في فتح واتساب',
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            icon: const Icon(Icons.chat),
            label: const Text('واتساب'),
          ),
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }
  
  // Show verification code input dialog
  static Future<bool> showVerificationCodeDialog(String phoneNumber) async {
    final codeController = TextEditingController();
    bool isLoading = false;
    
    return await Get.dialog<bool>(
      StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: const Text('أدخل رمز التحقق'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('تم إرسال رمز التحقق إلى:\n$phoneNumber'),
                const SizedBox(height: 20),
                TextField(
                  controller: codeController,
                  keyboardType: TextInputType.number,
                  maxLength: 6,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 8,
                  ),
                  decoration: const InputDecoration(
                    hintText: '000000',
                    counterText: '',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 10),
                const Text(
                  'الرمز صالح لمدة 5 دقائق',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: isLoading ? null : () => Get.back(result: false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () async {
                  final success = await sendVerificationCodeSMS(phoneNumber);
                  if (success) {
                    Get.snackbar(
                      'تم الإرسال',
                      'تم إعادة إرسال رمز التحقق',
                      backgroundColor: Colors.blue,
                      colorText: Colors.white,
                    );
                  }
                },
                child: const Text('إعادة إرسال'),
              ),
              ElevatedButton(
                onPressed: isLoading ? null : () async {
                  if (codeController.text.length != 6) {
                    Get.snackbar(
                      'خطأ',
                      'يرجى إدخال رمز التحقق كاملاً (6 أرقام)',
                      backgroundColor: Colors.orange,
                      colorText: Colors.white,
                    );
                    return;
                  }
                  
                  setState(() {
                    isLoading = true;
                  });
                  
                  final isValid = await verifyCode(phoneNumber, codeController.text);
                  
                  setState(() {
                    isLoading = false;
                  });
                  
                  if (isValid) {
                    Get.back(result: true);
                    Get.snackbar(
                      'تم التحقق',
                      'تم التحقق من رقم الهاتف بنجاح',
                      backgroundColor: Colors.green,
                      colorText: Colors.white,
                    );
                  } else {
                    Get.snackbar(
                      'خطأ',
                      'رمز التحقق غير صحيح أو منتهي الصلاحية',
                      backgroundColor: Colors.red,
                      colorText: Colors.white,
                    );
                  }
                },
                child: isLoading 
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('تحقق'),
              ),
            ],
          );
        },
      ),
    ) ?? false;
  }
}
