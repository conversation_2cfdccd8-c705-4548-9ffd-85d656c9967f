import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';

// وظائف التحقق المساعدة
String? validateEmail(String? value) {
  if (value == null || value.isEmpty) {
    return 'يرجى إدخال البريد الإلكتروني';
  }

  value = value.trim();

  if (!GetUtils.isEmail(value)) {
    return 'يرجى إدخال بريد إلكتروني صحيح';
  }

  if (!value.endsWith('@horus.edu.eg')) {
    return 'يجب استخدام بريد إلكتروني من نطاق @horus.edu.eg';
  }

  return null;
}

String? validatePassword(String? value) {
  if (value == null || value.isEmpty) {
    return 'يرجى إدخال كلمة المرور';
  }

  if (value.length < 8) {
    return 'كلمة المرور يجب أن تكون على الأقل 8 أحرف';
  }

  if (!isPasswordStrong(value)) {
    return 'كلمة المرور يجب أن تحتوي على أحرف وأرقام';
  }

  return null;
}

bool isPasswordStrong(String password) {
  final hasLetters = password.contains(RegExp(r'[a-zA-Z]'));
  final hasNumbers = password.contains(RegExp(r'[0-9]'));

  return hasLetters && hasNumbers;
}

void main() {
  group('Login Validation Tests', () {

    // اختبار بسيط للتحقق من صحة البريد الإلكتروني
    test('Email validation should work correctly', () {
      // اختبار بريد إلكتروني فارغ
      expect(validateEmail(''), 'يرجى إدخال البريد الإلكتروني');

      // اختبار بريد إلكتروني غير صحيح
      expect(validateEmail('invalid-email'), 'يرجى إدخال بريد إلكتروني صحيح');

      // اختبار نطاق غير صحيح
      expect(validateEmail('<EMAIL>'), 'يجب استخدام بريد إلكتروني من نطاق @horus.edu.eg');

      // اختبار بريد إلكتروني صحيح
      expect(validateEmail('<EMAIL>'), null);
    });

    // اختبار التحقق من صحة كلمة المرور
    test('Password validation should work correctly', () {
      // اختبار كلمة مرور فارغة
      expect(validatePassword(''), 'يرجى إدخال كلمة المرور');

      // اختبار كلمة مرور قصيرة
      expect(validatePassword('123'), 'كلمة المرور يجب أن تكون على الأقل 8 أحرف');

      // اختبار كلمة مرور ضعيفة (أرقام فقط)
      expect(validatePassword('12345678'), 'كلمة المرور يجب أن تحتوي على أحرف وأرقام');

      // اختبار كلمة مرور ضعيفة (أحرف فقط)
      expect(validatePassword('abcdefgh'), 'كلمة المرور يجب أن تحتوي على أحرف وأرقام');

      // اختبار كلمة مرور صحيحة
      expect(validatePassword('abc12345'), null);
    });

    // اختبار قوة كلمة المرور
    test('Password strength check should work correctly', () {
      expect(isPasswordStrong('12345678'), false); // أرقام فقط
      expect(isPasswordStrong('abcdefgh'), false); // أحرف فقط
      expect(isPasswordStrong('abc12345'), true);  // أحرف وأرقام
      expect(isPasswordStrong('Test123!'), true);  // أحرف وأرقام ورموز
    });
  });
}
