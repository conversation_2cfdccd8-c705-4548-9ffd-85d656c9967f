import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';

// وظائف التحقق المساعدة للتسجيل متعدد الخطوات

// التحقق من الاسم
String? validateName(String? value) {
  if (value == null || value.trim().isEmpty) {
    return 'يرجى إدخال الاسم';
  }
  if (value.trim().length < 2) {
    return 'الاسم يجب أن يكون أكثر من حرف واحد';
  }
  return null;
}

// التحقق من البريد الإلكتروني
String? validateEmail(String? value) {
  if (value == null || value.isEmpty) {
    return 'يرجى إدخال البريد الإلكتروني';
  }
  
  value = value.trim();
  
  if (!GetUtils.isEmail(value)) {
    return 'يرجى إدخال بريد إلكتروني صحيح';
  }
  
  if (!value.endsWith('@horus.edu.eg')) {
    return 'يجب استخدام بريد إلكتروني من نطاق @horus.edu.eg';
  }
  
  return null;
}

// التحقق من رقم الهاتف
String? validatePhone(String? value) {
  if (value == null || value.trim().isEmpty) {
    return 'يرجى إدخال رقم الهاتف';
  }
  
  value = value.trim();
  
  if (value.length < 11) {
    return 'رقم الهاتف يجب أن يكون 11 رقم';
  }
  
  if (!value.startsWith('01')) {
    return 'رقم الهاتف يجب أن يبدأ بـ 01';
  }
  
  return null;
}

// التحقق من كلمة المرور
String? validatePassword(String? value) {
  if (value == null || value.isEmpty) {
    return 'يرجى إدخال كلمة المرور';
  }
  
  if (value.length < 8) {
    return 'كلمة المرور يجب أن تكون على الأقل 8 أحرف';
  }
  
  if (!isPasswordStrong(value)) {
    return 'كلمة المرور يجب أن تحتوي على أحرف وأرقام';
  }
  
  return null;
}

// التحقق من تطابق كلمة المرور
String? validatePasswordConfirmation(String? value, String password) {
  if (value == null || value.isEmpty) {
    return 'يرجى تأكيد كلمة المرور';
  }
  
  if (value != password) {
    return 'كلمة المرور وتأكيد كلمة المرور لا يتطابقان';
  }
  
  return null;
}

// التحقق من العمر
String? validateAge(String? value) {
  if (value == null || value.trim().isEmpty) {
    return 'يرجى إدخال العمر';
  }
  
  final age = int.tryParse(value.trim());
  if (age == null) {
    return 'يرجى إدخال عمر صحيح';
  }
  
  if (age < 18) {
    return 'يجب أن تكون 18 سنة أو أكثر';
  }
  
  if (age > 100) {
    return 'يرجى إدخال عمر صحيح';
  }
  
  return null;
}

// التحقق من قوة كلمة المرور
bool isPasswordStrong(String password) {
  final hasLetters = password.contains(RegExp(r'[a-zA-Z]'));
  final hasNumbers = password.contains(RegExp(r'[0-9]'));
  
  return hasLetters && hasNumbers;
}

void main() {
  group('Multi-Step Signup Validation Tests', () {
    
    // اختبار التحقق من الاسم
    test('Name validation should work correctly', () {
      // اختبار اسم فارغ
      expect(validateName(''), 'يرجى إدخال الاسم');
      expect(validateName(null), 'يرجى إدخال الاسم');
      
      // اختبار اسم قصير
      expect(validateName('أ'), 'الاسم يجب أن يكون أكثر من حرف واحد');
      
      // اختبار اسم صحيح
      expect(validateName('أحمد'), null);
      expect(validateName('محمد علي'), null);
    });

    // اختبار التحقق من البريد الإلكتروني
    test('Email validation should work correctly', () {
      // اختبار بريد إلكتروني فارغ
      expect(validateEmail(''), 'يرجى إدخال البريد الإلكتروني');
      
      // اختبار بريد إلكتروني غير صحيح
      expect(validateEmail('invalid-email'), 'يرجى إدخال بريد إلكتروني صحيح');
      
      // اختبار نطاق غير صحيح
      expect(validateEmail('<EMAIL>'), 'يجب استخدام بريد إلكتروني من نطاق @horus.edu.eg');
      
      // اختبار بريد إلكتروني صحيح
      expect(validateEmail('<EMAIL>'), null);
    });

    // اختبار التحقق من رقم الهاتف
    test('Phone validation should work correctly', () {
      // اختبار رقم فارغ
      expect(validatePhone(''), 'يرجى إدخال رقم الهاتف');
      
      // اختبار رقم قصير
      expect(validatePhone('123'), 'رقم الهاتف يجب أن يكون 11 رقم');
      
      // اختبار رقم لا يبدأ بـ 01
      expect(validatePhone('12345678901'), 'رقم الهاتف يجب أن يبدأ بـ 01');
      
      // اختبار رقم صحيح
      expect(validatePhone('01234567890'), null);
      expect(validatePhone('01012345678'), null);
    });

    // اختبار التحقق من كلمة المرور
    test('Password validation should work correctly', () {
      // اختبار كلمة مرور فارغة
      expect(validatePassword(''), 'يرجى إدخال كلمة المرور');
      
      // اختبار كلمة مرور قصيرة
      expect(validatePassword('123'), 'كلمة المرور يجب أن تكون على الأقل 8 أحرف');
      
      // اختبار كلمة مرور ضعيفة
      expect(validatePassword('12345678'), 'كلمة المرور يجب أن تحتوي على أحرف وأرقام');
      expect(validatePassword('abcdefgh'), 'كلمة المرور يجب أن تحتوي على أحرف وأرقام');
      
      // اختبار كلمة مرور صحيحة
      expect(validatePassword('abc12345'), null);
    });

    // اختبار تأكيد كلمة المرور
    test('Password confirmation should work correctly', () {
      // اختبار تأكيد فارغ
      expect(validatePasswordConfirmation('', 'password123'), 'يرجى تأكيد كلمة المرور');
      
      // اختبار عدم تطابق
      expect(validatePasswordConfirmation('different', 'password123'), 'كلمة المرور وتأكيد كلمة المرور لا يتطابقان');
      
      // اختبار تطابق صحيح
      expect(validatePasswordConfirmation('password123', 'password123'), null);
    });

    // اختبار التحقق من العمر
    test('Age validation should work correctly', () {
      // اختبار عمر فارغ
      expect(validateAge(''), 'يرجى إدخال العمر');
      
      // اختبار عمر غير صحيح
      expect(validateAge('abc'), 'يرجى إدخال عمر صحيح');
      
      // اختبار عمر أقل من 18
      expect(validateAge('17'), 'يجب أن تكون 18 سنة أو أكثر');
      
      // اختبار عمر أكبر من 100
      expect(validateAge('150'), 'يرجى إدخال عمر صحيح');
      
      // اختبار عمر صحيح
      expect(validateAge('20'), null);
      expect(validateAge('25'), null);
    });

    // اختبار قوة كلمة المرور
    test('Password strength check should work correctly', () {
      expect(isPasswordStrong('12345678'), false); // أرقام فقط
      expect(isPasswordStrong('abcdefgh'), false); // أحرف فقط
      expect(isPasswordStrong('abc12345'), true);  // أحرف وأرقام
      expect(isPasswordStrong('Test123!'), true);  // أحرف وأرقام ورموز
    });
  });
}
