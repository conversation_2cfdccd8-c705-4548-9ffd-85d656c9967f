import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_design_system.dart';

/// بطاقة محسنة مع تأثيرات بصرية جميلة
class EnhancedCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? elevation;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final bool enableHoverEffect;
  final bool enableTapEffect;
  final Duration animationDuration;

  const EnhancedCard({
    super.key,
    required this.child,
    this.onTap,
    this.padding,
    this.margin,
    this.elevation,
    this.backgroundColor,
    this.borderRadius,
    this.enableHoverEffect = true,
    this.enableTapEffect = true,
    this.animationDuration = AppDesignSystem.animationDuration,
  });

  @override
  State<EnhancedCard> createState() => _EnhancedCardState();
}

class _EnhancedCardState extends State<EnhancedCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  
  bool _isHovered = false;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: AppDesignSystem.animationCurve,
    ));
    
    _elevationAnimation = Tween<double>(
      begin: widget.elevation ?? 2.0,
      end: (widget.elevation ?? 2.0) + 4.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: AppDesignSystem.animationCurve,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.enableTapEffect) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (widget.enableTapEffect) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  void _handleTapCancel() {
    if (widget.enableTapEffect) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  void _handleHoverEnter(PointerEnterEvent event) {
    if (widget.enableHoverEffect && mounted) {
      setState(() => _isHovered = true);
      if (!_isPressed) {
        _animationController.forward();
      }
    }
  }

  void _handleHoverExit(PointerExitEvent event) {
    if (widget.enableHoverEffect && mounted) {
      setState(() => _isHovered = false);
      if (!_isPressed) {
        _animationController.reverse();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: widget.margin ?? const EdgeInsets.all(AppDesignSystem.spacingSmall),
      child: MouseRegion(
        onEnter: _handleHoverEnter,
        onExit: _handleHoverExit,
        child: GestureDetector(
          onTapDown: _handleTapDown,
          onTapUp: _handleTapUp,
          onTapCancel: _handleTapCancel,
          onTap: widget.onTap,
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Transform.scale(
                scale: widget.enableTapEffect && _isPressed 
                    ? _scaleAnimation.value 
                    : 1.0,
                child: AnimatedContainer(
                  duration: widget.animationDuration,
                  curve: AppDesignSystem.animationCurve,
                  decoration: BoxDecoration(
                    color: widget.backgroundColor ?? AppDesignSystem.cardColor,
                    borderRadius: widget.borderRadius ?? 
                        BorderRadius.circular(AppDesignSystem.radiusMedium),
                    boxShadow: [
                      BoxShadow(
                        color: AppDesignSystem.primaryColor.withValues(alpha: 0.1),
                        blurRadius: _isHovered 
                            ? _elevationAnimation.value 
                            : (widget.elevation ?? 2.0),
                        offset: Offset(0, _isHovered ? 4 : 2),
                        spreadRadius: _isHovered ? 1 : 0,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: widget.borderRadius ?? 
                        BorderRadius.circular(AppDesignSystem.radiusMedium),
                    child: Container(
                      padding: widget.padding ?? 
                          const EdgeInsets.all(AppDesignSystem.spacingMedium),
                      child: widget.child,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
