import 'package:flutter/material.dart';
import '../theme/app_design_system.dart';

enum ButtonType { primary, secondary, outline, text, gradient }
enum ButtonSize { small, medium, large }

/// زر محسن مع تأثيرات بصرية جميلة
class EnhancedButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final ButtonSize size;
  final IconData? icon;
  final bool isLoading;
  final bool isFullWidth;
  final Color? customColor;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;

  const EnhancedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.customColor,
    this.margin,
    this.borderRadius,
  });

  @override
  State<EnhancedButton> createState() => _EnhancedButtonState();
}

class _EnhancedButtonState extends State<EnhancedButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rippleAnimation;
  
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppDesignSystem.fastAnimationDuration,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: AppDesignSystem.fastAnimationCurve,
    ));
    
    _rippleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (mounted) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (mounted) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  void _handleTapCancel() {
    if (mounted) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  Color _getButtonColor() {
    if (widget.customColor != null) return widget.customColor!;
    
    switch (widget.type) {
      case ButtonType.primary:
        return AppDesignSystem.primaryColor;
      case ButtonType.secondary:
        return AppDesignSystem.secondaryColor;
      case ButtonType.outline:
        return Colors.transparent;
      case ButtonType.text:
        return Colors.transparent;
      case ButtonType.gradient:
        return AppDesignSystem.primaryColor;
    }
  }

  Color _getTextColor() {
    switch (widget.type) {
      case ButtonType.primary:
      case ButtonType.secondary:
      case ButtonType.gradient:
        return Colors.white;
      case ButtonType.outline:
      case ButtonType.text:
        return widget.customColor ?? AppDesignSystem.primaryColor;
    }
  }

  EdgeInsetsGeometry _getPadding() {
    switch (widget.size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(
          horizontal: AppDesignSystem.spacingMedium,
          vertical: AppDesignSystem.spacingSmall,
        );
      case ButtonSize.medium:
        return const EdgeInsets.symmetric(
          horizontal: AppDesignSystem.spacingLarge,
          vertical: AppDesignSystem.spacingMedium,
        );
      case ButtonSize.large:
        return const EdgeInsets.symmetric(
          horizontal: AppDesignSystem.spacingXLarge,
          vertical: AppDesignSystem.spacingLarge,
        );
    }
  }

  double _getFontSize() {
    switch (widget.size) {
      case ButtonSize.small:
        return AppDesignSystem.fontSizeMedium;
      case ButtonSize.medium:
        return AppDesignSystem.fontSizeLarge;
      case ButtonSize.large:
        return AppDesignSystem.fontSizeXLarge;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEnabled = widget.onPressed != null && !widget.isLoading;
    
    Widget buttonChild = Row(
      mainAxisSize: widget.isFullWidth ? MainAxisSize.max : MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (widget.isLoading) ...[
          SizedBox(
            width: _getFontSize(),
            height: _getFontSize(),
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(_getTextColor()),
            ),
          ),
          const SizedBox(width: AppDesignSystem.spacingSmall),
        ] else if (widget.icon != null) ...[
          Icon(
            widget.icon,
            size: _getFontSize(),
            color: _getTextColor(),
          ),
          const SizedBox(width: AppDesignSystem.spacingSmall),
        ],
        Text(
          widget.text,
          style: TextStyle(
            fontSize: _getFontSize(),
            fontWeight: AppDesignSystem.fontWeightMedium,
            color: _getTextColor(),
          ),
        ),
      ],
    );

    Widget button = Container(
      margin: widget.margin,
      width: widget.isFullWidth ? double.infinity : null,
      child: GestureDetector(
        onTapDown: isEnabled ? _handleTapDown : null,
        onTapUp: isEnabled ? _handleTapUp : null,
        onTapCancel: isEnabled ? _handleTapCancel : null,
        onTap: isEnabled ? widget.onPressed : null,
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.scale(
              scale: _isPressed ? _scaleAnimation.value : 1.0,
              child: Container(
                padding: _getPadding(),
                decoration: BoxDecoration(
                  color: widget.type == ButtonType.gradient ? null : _getButtonColor(),
                  gradient: widget.type == ButtonType.gradient
                      ? AppDesignSystem.primaryGradient
                      : null,
                  borderRadius: widget.borderRadius ??
                      BorderRadius.circular(AppDesignSystem.radiusMedium),
                  border: widget.type == ButtonType.outline
                      ? Border.all(
                          color: widget.customColor ?? AppDesignSystem.primaryColor,
                          width: 1.5,
                        )
                      : null,
                  boxShadow: widget.type != ButtonType.text && widget.type != ButtonType.outline
                      ? [
                          BoxShadow(
                            color: _getButtonColor().withValues(alpha: 0.3),
                            blurRadius: _isPressed ? 8 : 4,
                            offset: Offset(0, _isPressed ? 4 : 2),
                          ),
                        ]
                      : null,
                ),
                child: Stack(
                  children: [
                    buttonChild,
                    if (_isPressed)
                      Positioned.fill(
                        child: AnimatedBuilder(
                          animation: _rippleAnimation,
                          builder: (context, child) {
                            return Container(
                              decoration: BoxDecoration(
                                borderRadius: widget.borderRadius ??
                                    BorderRadius.circular(AppDesignSystem.radiusMedium),
                                color: Colors.white.withValues(
                                  alpha: 0.2 * _rippleAnimation.value,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );

    if (!isEnabled) {
      return Opacity(
        opacity: 0.6,
        child: button,
      );
    }

    return button;
  }
}
