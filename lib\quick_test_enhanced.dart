import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hue/core/theme/app_design_system.dart';
import 'package:hue/core/widgets/enhanced_button.dart';
import 'package:hue/core/widgets/enhanced_card.dart';
import 'package:hue/core/widgets/enhanced_text_field.dart';
import 'package:hue/core/widgets/enhanced_loading.dart';
import 'package:hue/Pages/home/<USER>';
import 'package:hue/Pages/auth/enhanced_login.dart';

/// اختبار سريع للمكونات المحسنة
class QuickTestEnhanced extends StatefulWidget {
  const QuickTestEnhanced({super.key});

  @override
  State<QuickTestEnhanced> createState() => _QuickTestEnhancedState();
}

class _QuickTestEnhancedState extends State<QuickTestEnhanced> {
  final _textController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار المكونات المحسنة'),
        backgroundColor: AppDesignSystem.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppDesignSystem.backgroundGradient,
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Test Enhanced Cards
              const Text(
                'البطاقات المحسنة',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppDesignSystem.primaryColor,
                ),
              ),
              const SizedBox(height: 16),
              
              EnhancedCard(
                onTap: () => _showMessage('تم النقر على البطاقة!'),
                child: const Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Icon(Icons.touch_app, size: 40, color: AppDesignSystem.primaryColor),
                      SizedBox(height: 8),
                      Text('بطاقة تفاعلية'),
                      Text('اضغط لاختبار التأثيرات', style: TextStyle(fontSize: 12)),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Test Enhanced Buttons
              const Text(
                'الأزرار المحسنة',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppDesignSystem.primaryColor,
                ),
              ),
              const SizedBox(height: 16),
              
              EnhancedButton(
                text: 'زر أساسي',
                type: ButtonType.primary,
                icon: Icons.star,
                onPressed: () => _showMessage('زر أساسي!'),
              ),
              
              const SizedBox(height: 12),
              
              EnhancedButton(
                text: 'زر متدرج',
                type: ButtonType.gradient,
                icon: Icons.gradient,
                onPressed: () => _showMessage('زر متدرج!'),
              ),
              
              const SizedBox(height: 12),
              
              EnhancedButton(
                text: 'زر التحميل',
                type: ButtonType.secondary,
                icon: Icons.download,
                isLoading: _isLoading,
                onPressed: _isLoading ? null : _testLoading,
              ),
              
              const SizedBox(height: 24),
              
              // Test Enhanced Text Fields
              const Text(
                'حقول الإدخال المحسنة',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppDesignSystem.primaryColor,
                ),
              ),
              const SizedBox(height: 16),
              
              EnhancedTextField(
                controller: _textController,
                label: 'اختبار النص',
                hint: 'اكتب شيئاً هنا',
                prefixIcon: Icons.edit,
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'يرجى إدخال نص';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              EnhancedTextField(
                label: 'كلمة المرور',
                hint: 'أدخل كلمة المرور',
                prefixIcon: Icons.lock,
                suffixIcon: Icons.visibility,
                obscureText: true,
              ),
              
              const SizedBox(height: 24),
              
              // Test Enhanced Loading
              const Text(
                'مكونات التحميل',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppDesignSystem.primaryColor,
                ),
              ),
              const SizedBox(height: 16),
              
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Column(
                    children: [
                      const EnhancedLoading(
                        type: LoadingType.circular,
                        size: 40,
                      ),
                      const SizedBox(height: 8),
                      Text('دائري', style: TextStyle(fontSize: 12)),
                    ],
                  ),
                  Column(
                    children: [
                      const EnhancedLoading(
                        type: LoadingType.dots,
                        size: 40,
                      ),
                      const SizedBox(height: 8),
                      Text('نقاط', style: TextStyle(fontSize: 12)),
                    ],
                  ),
                  Column(
                    children: [
                      const EnhancedLoading(
                        type: LoadingType.pulse,
                        size: 40,
                      ),
                      const SizedBox(height: 8),
                      Text('نبضة', style: TextStyle(fontSize: 12)),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 24),
              
              // Navigation Tests
              const Text(
                'اختبار التنقل',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppDesignSystem.primaryColor,
                ),
              ),
              const SizedBox(height: 16),
              
              EnhancedButton(
                text: 'صفحة الترحيب المحسنة',
                type: ButtonType.outline,
                icon: Icons.home,
                onPressed: () => Get.to(() => const EnhancedWelcomePage()),
              ),
              
              const SizedBox(height: 12),
              
              EnhancedButton(
                text: 'صفحة تسجيل الدخول المحسنة',
                type: ButtonType.outline,
                icon: Icons.login,
                onPressed: () => Get.to(() => const EnhancedLogin()),
              ),
              
              const SizedBox(height: 24),
              
              // Success Message
              EnhancedCard(
                backgroundColor: AppDesignSystem.successColor.withValues(alpha: 0.1),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      const Icon(Icons.check_circle, color: AppDesignSystem.successColor),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'تم الإصلاح بنجاح!',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: AppDesignSystem.successColor,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'جميع المكونات المحسنة تعمل بشكل صحيح',
                              style: TextStyle(
                                fontSize: 12,
                                color: AppDesignSystem.successColor.withValues(alpha: 0.8),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showMessage(String message) {
    Get.snackbar(
      'اختبار ناجح',
      message,
      backgroundColor: AppDesignSystem.primaryColor,
      colorText: Colors.white,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  Future<void> _testLoading() async {
    setState(() => _isLoading = true);
    
    // محاكاة عملية تحميل
    await Future.delayed(const Duration(seconds: 3));
    
    if (mounted) {
      setState(() => _isLoading = false);
      _showMessage('تم الانتهاء من التحميل!');
    }
  }
}
