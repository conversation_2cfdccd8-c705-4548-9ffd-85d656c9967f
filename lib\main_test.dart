import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hue/core/theme/app_theme.dart';
import 'package:hue/test_enhanced_components.dart';

/// ملف تشغيل اختبار للمكونات المحسنة
void main() {
  runApp(const TestApp());
}

class TestApp extends StatelessWidget {
  const TestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'اختبار المكونات المحسنة - HUE',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      home: const TestEnhancedComponents(),
      locale: const Locale('ar', 'SA'),
      fallbackLocale: const Locale('en', 'US'),
    );
  }
}
