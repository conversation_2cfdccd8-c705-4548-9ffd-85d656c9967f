import 'dart:io';

void main() async {
  print('🔧 بدء إصلاح جميع مشاكل التطبيق...\n');

  // 1. إصلاح مشاكل withOpacity
  await fixWithOpacityIssues();
  
  // 2. إصلاح مشاكل const constructors
  await fixConstConstructorIssues();
  
  // 3. إصلاح مشاكل super parameters
  await fixSuperParameterIssues();
  
  // 4. إصلاح مشاكل أسماء الملفات
  await fixFileNameIssues();
  
  // 5. إصلاح مشاكل أسماء الثوابت
  await fixConstantNameIssues();
  
  // 6. إزالة الاستيرادات غير المستخدمة
  await removeUnusedImports();
  
  print('\n✅ تم إصلاح جميع المشاكل بنجاح!');
  print('🚀 يمكنك الآن تشغيل flutter analyze للتحقق من النتائج');
}

Future<void> fixWithOpacityIssues() async {
  print('🔄 إصلاح مشاكل withOpacity...');
  
  final result = await Process.run('find', [
    'lib',
    '-name',
    '*.dart',
    '-exec',
    'sed',
    '-i',
    's/\\.withOpacity(\\([^)]*\\))/.withValues(alpha: \\1)/g',
    '{}',
    ';'
  ]);
  
  if (result.exitCode == 0) {
    print('✅ تم إصلاح مشاكل withOpacity');
  } else {
    print('❌ فشل في إصلاح مشاكل withOpacity: ${result.stderr}');
  }
}

Future<void> fixConstConstructorIssues() async {
  print('🔄 إصلاح مشاكل const constructors...');
  
  // هذا يتطلب معالجة يدوية أكثر تعقيداً
  print('⚠️  مشاكل const constructors تحتاج إصلاح يدوي');
}

Future<void> fixSuperParameterIssues() async {
  print('🔄 إصلاح مشاكل super parameters...');
  
  // إصلاح Key? key إلى super.key
  final result = await Process.run('find', [
    'lib',
    '-name',
    '*.dart',
    '-exec',
    'sed',
    '-i',
    's/{Key\\? key}/{super.key}/g',
    '{}',
    ';'
  ]);
  
  if (result.exitCode == 0) {
    print('✅ تم إصلاح مشاكل super parameters');
  } else {
    print('❌ فشل في إصلاح مشاكل super parameters: ${result.stderr}');
  }
}

Future<void> fixFileNameIssues() async {
  print('🔄 إصلاح أسماء الملفات...');
  
  final filesToRename = [
    'lib/Pages/college/departments/Applied Health Sciences.dart',
    'lib/Pages/college/departments/Business Admin.dart',
    'lib/Pages/college/departments/Dentistry.dart',
    'lib/Pages/college/departments/Engineering.dart',
    'lib/Pages/college/departments/Fine Arts.dart',
    'lib/Pages/college/departments/Human Medicine.dart',
    'lib/Pages/college/departments/Linguistics.dart',
    'lib/Pages/college/departments/Pharmacy.dart',
    'lib/Pages/college/departments/Physical Therapy.dart',
    'lib/Pages/education/doctors/DoctorProfilePage.dart',
    'lib/Pages/education/stuodents/Academic_Support.dart',
    'lib/Pages/education/stuodents/Fee_Management.dart',
    'lib/Pages/staff/Admin.dart',
  ];
  
  for (final oldPath in filesToRename) {
    final file = File(oldPath);
    if (await file.exists()) {
      final newPath = oldPath
          .replaceAll(' ', '_')
          .toLowerCase()
          .replaceAll('admin.dart', 'admin.dart')
          .replaceAll('doctorprofilepage.dart', 'doctor_profile_page.dart');
      
      try {
        await file.rename(newPath);
        print('✅ تم إعادة تسمية: $oldPath -> $newPath');
      } catch (e) {
        print('❌ فشل في إعادة تسمية $oldPath: $e');
      }
    }
  }
}

Future<void> fixConstantNameIssues() async {
  print('🔄 إصلاح أسماء الثوابت...');
  
  final constantFixes = {
    'all_admin': 'allAdmin',
    'all_student': 'allStudent',
    'all_staff': 'allStaff',
    'all_faculty': 'allFaculty',
    'student_admin': 'studentAdmin',
    'student_manager': 'studentManager',
  };
  
  for (final entry in constantFixes.entries) {
    final result = await Process.run('find', [
      'lib',
      '-name',
      '*.dart',
      '-exec',
      'sed',
      '-i',
      's/${entry.key}/${entry.value}/g',
      '{}',
      ';'
    ]);
    
    if (result.exitCode == 0) {
      print('✅ تم إصلاح: ${entry.key} -> ${entry.value}');
    }
  }
}

Future<void> removeUnusedImports() async {
  print('🔄 إزالة الاستيرادات غير المستخدمة...');
  
  // قائمة بالاستيرادات غير المستخدمة المعروفة
  final unusedImports = [
    "import 'package:get/get.dart';",
    "import 'package:hue/core/utils/app_colors.dart';",
    "import 'package:flutter/material.dart';",
    "import 'package:hue/core/services/error_handler.dart';",
    "import 'package:http/http.dart' as http;",
    "import 'dart:convert';",
    "import 'package:path/path.dart';",
  ];
  
  print('⚠️  إزالة الاستيرادات غير المستخدمة تحتاج فحص يدوي');
  print('💡 استخدم flutter analyze لتحديد الاستيرادات غير المستخدمة');
}
