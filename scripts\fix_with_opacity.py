#!/usr/bin/env python3
import os
import re
import glob

def fix_with_opacity_in_file(file_path):
    """إصلاح مشاكل withOpacity في ملف واحد"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن جميع استخدامات withOpacity وتحويلها إلى withValues
        pattern = r'\.withOpacity\(([^)]+)\)'
        replacement = r'.withValues(alpha: \1)'
        
        new_content = re.sub(pattern, replacement, content)
        
        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"✅ تم إصلاح: {file_path}")
            return True
        else:
            return False
    except Exception as e:
        print(f"❌ خطأ في إصلاح {file_path}: {e}")
        return False

def main():
    print("🔧 بدء إصلاح مشاكل withOpacity في جميع ملفات Dart...")
    
    # البحث عن جميع ملفات .dart في مجلد lib
    dart_files = glob.glob('lib/**/*.dart', recursive=True)
    
    fixed_count = 0
    total_count = len(dart_files)
    
    for file_path in dart_files:
        if fix_with_opacity_in_file(file_path):
            fixed_count += 1
    
    print(f"\n📊 النتائج:")
    print(f"   📁 إجمالي الملفات: {total_count}")
    print(f"   ✅ الملفات المُصلحة: {fixed_count}")
    print(f"   ⏭️  الملفات بدون تغيير: {total_count - fixed_count}")
    
    if fixed_count > 0:
        print(f"\n🎉 تم إصلاح {fixed_count} ملف بنجاح!")
    else:
        print(f"\n✨ جميع الملفات سليمة بالفعل!")

if __name__ == "__main__":
    main()
