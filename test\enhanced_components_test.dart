import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:hue/core/widgets/enhanced_card.dart';
import 'package:hue/core/widgets/enhanced_button.dart';
import 'package:hue/core/widgets/enhanced_text_field.dart';
import 'package:hue/core/widgets/enhanced_loading.dart';
import 'package:hue/core/theme/app_design_system.dart';

void main() {
  group('Enhanced Components Tests', () {
    
    testWidgets('EnhancedCard should render correctly', (WidgetTester tester) async {
      bool tapped = false;
      
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(
            body: EnhancedCard(
              onTap: () => tapped = true,
              child: const Text('Test Card'),
            ),
          ),
        ),
      );

      // Verify card is rendered
      expect(find.text('Test Card'), findsOneWidget);
      expect(find.byType(EnhancedCard), findsOneWidget);

      // Test tap functionality
      await tester.tap(find.byType(EnhancedCard));
      await tester.pump();
      
      expect(tapped, isTrue);
    });

    testWidgets('EnhancedButton should render correctly', (WidgetTester tester) async {
      bool pressed = false;
      
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(
            body: EnhancedButton(
              text: 'Test Button',
              onPressed: () => pressed = true,
            ),
          ),
        ),
      );

      // Verify button is rendered
      expect(find.text('Test Button'), findsOneWidget);
      expect(find.byType(EnhancedButton), findsOneWidget);

      // Test press functionality
      await tester.tap(find.byType(EnhancedButton));
      await tester.pump();
      
      expect(pressed, isTrue);
    });

    testWidgets('EnhancedButton with loading state', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(
            body: EnhancedButton(
              text: 'Loading Button',
              isLoading: true,
              onPressed: () {},
            ),
          ),
        ),
      );

      // Verify loading indicator is shown
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Loading Button'), findsOneWidget);
    });

    testWidgets('EnhancedTextField should render correctly', (WidgetTester tester) async {
      final controller = TextEditingController();
      
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(
            body: EnhancedTextField(
              controller: controller,
              label: 'Test Field',
              hint: 'Enter text',
            ),
          ),
        ),
      );

      // Verify text field is rendered
      expect(find.byType(EnhancedTextField), findsOneWidget);
      expect(find.text('Test Field'), findsOneWidget);

      // Test text input
      await tester.enterText(find.byType(TextFormField), 'Hello World');
      expect(controller.text, 'Hello World');
    });

    testWidgets('EnhancedTextField validation', (WidgetTester tester) async {
      final controller = TextEditingController();
      
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(
            body: Form(
              child: EnhancedTextField(
                controller: controller,
                label: 'Email',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Required field';
                  }
                  return null;
                },
              ),
            ),
          ),
        ),
      );

      // Test validation with empty field
      final formState = tester.state<FormState>(find.byType(Form));
      expect(formState.validate(), isFalse);

      // Test validation with valid input
      await tester.enterText(find.byType(TextFormField), '<EMAIL>');
      expect(formState.validate(), isTrue);
    });

    testWidgets('EnhancedLoading should render correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        const GetMaterialApp(
          home: Scaffold(
            body: EnhancedLoading(
              type: LoadingType.circular,
              message: 'Loading...',
              showMessage: true,
            ),
          ),
        ),
      );

      // Verify loading component is rendered
      expect(find.byType(EnhancedLoading), findsOneWidget);
      expect(find.text('Loading...'), findsOneWidget);
    });

    testWidgets('EnhancedLoading different types', (WidgetTester tester) async {
      for (final type in LoadingType.values) {
        await tester.pumpWidget(
          GetMaterialApp(
            home: Scaffold(
              body: EnhancedLoading(
                type: type,
                size: 50,
              ),
            ),
          ),
        );

        // Verify loading component is rendered for each type
        expect(find.byType(EnhancedLoading), findsOneWidget);
        
        // Let animations run
        await tester.pump(const Duration(milliseconds: 100));
      }
    });

    group('Button Types Tests', () {
      for (final type in ButtonType.values) {
        testWidgets('EnhancedButton type $type should render', (WidgetTester tester) async {
          await tester.pumpWidget(
            GetMaterialApp(
              home: Scaffold(
                body: EnhancedButton(
                  text: 'Test $type',
                  type: type,
                  onPressed: () {},
                ),
              ),
            ),
          );

          expect(find.byType(EnhancedButton), findsOneWidget);
          expect(find.text('Test $type'), findsOneWidget);
        });
      }
    });

    group('Button Sizes Tests', () {
      for (final size in ButtonSize.values) {
        testWidgets('EnhancedButton size $size should render', (WidgetTester tester) async {
          await tester.pumpWidget(
            GetMaterialApp(
              home: Scaffold(
                body: EnhancedButton(
                  text: 'Test $size',
                  size: size,
                  onPressed: () {},
                ),
              ),
            ),
          );

          expect(find.byType(EnhancedButton), findsOneWidget);
          expect(find.text('Test $size'), findsOneWidget);
        });
      }
    });

    testWidgets('EnhancedCard hover effects', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(
            body: EnhancedCard(
              enableHoverEffect: true,
              child: const Text('Hover Card'),
            ),
          ),
        ),
      );

      expect(find.byType(EnhancedCard), findsOneWidget);
      expect(find.text('Hover Card'), findsOneWidget);

      // Test hover functionality (limited in widget tests)
      final cardWidget = tester.widget<EnhancedCard>(find.byType(EnhancedCard));
      expect(cardWidget.enableHoverEffect, isTrue);
    });

    testWidgets('EnhancedTextField with icons', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(
            body: EnhancedTextField(
              label: 'Password',
              prefixIcon: Icons.lock,
              suffixIcon: Icons.visibility,
              obscureText: true,
            ),
          ),
        ),
      );

      expect(find.byType(EnhancedTextField), findsOneWidget);
      expect(find.byIcon(Icons.lock), findsOneWidget);
      expect(find.byIcon(Icons.visibility), findsOneWidget);
    });

    testWidgets('EnhancedButton with icon', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(
            body: EnhancedButton(
              text: 'Icon Button',
              icon: Icons.star,
              onPressed: () {},
            ),
          ),
        ),
      );

      expect(find.byType(EnhancedButton), findsOneWidget);
      expect(find.text('Icon Button'), findsOneWidget);
      expect(find.byIcon(Icons.star), findsOneWidget);
    });

    testWidgets('EnhancedCard without tap effects', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(
            body: EnhancedCard(
              enableTapEffect: false,
              enableHoverEffect: false,
              child: const Text('Static Card'),
            ),
          ),
        ),
      );

      expect(find.byType(EnhancedCard), findsOneWidget);
      expect(find.text('Static Card'), findsOneWidget);

      final cardWidget = tester.widget<EnhancedCard>(find.byType(EnhancedCard));
      expect(cardWidget.enableTapEffect, isFalse);
      expect(cardWidget.enableHoverEffect, isFalse);
    });

    testWidgets('EnhancedTextField multiline', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          home: Scaffold(
            body: EnhancedTextField(
              label: 'Message',
              hint: 'Enter your message',
              maxLines: 3,
            ),
          ),
        ),
      );

      expect(find.byType(EnhancedTextField), findsOneWidget);
      
      final textField = tester.widget<TextFormField>(find.byType(TextFormField));
      expect(textField.maxLines, 3);
    });
  });
}
