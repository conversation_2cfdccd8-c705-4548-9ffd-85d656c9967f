import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_design_system.dart';

/// حقل إدخال محسن مع تأثيرات بصرية وتحقق من صحة البيانات
class EnhancedTextField extends StatefulWidget {
  final String? label;
  final String? hint;
  final String? helperText;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VoidCallback? onSuffixIconTap;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function(String)? onSubmitted;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final int? maxLines;
  final int? maxLength;
  final FocusNode? focusNode;
  final EdgeInsetsGeometry? margin;
  final bool showCharacterCount;
  final bool animateLabel;

  const EnhancedTextField({
    super.key,
    this.label,
    this.hint,
    this.helperText,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.controller,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.keyboardType,
    this.inputFormatters,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.maxLines = 1,
    this.maxLength,
    this.focusNode,
    this.margin,
    this.showCharacterCount = false,
    this.animateLabel = true,
  });

  @override
  State<EnhancedTextField> createState() => _EnhancedTextFieldState();
}

class _EnhancedTextFieldState extends State<EnhancedTextField>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _focusAnimation;
  late Animation<Color?> _borderColorAnimation;
  
  late FocusNode _focusNode;
  bool _isFocused = false;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);
    
    _animationController = AnimationController(
      duration: AppDesignSystem.animationDuration,
      vsync: this,
    );
    
    _focusAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: AppDesignSystem.animationCurve,
    ));
    
    _borderColorAnimation = ColorTween(
      begin: const Color(0xFFE0E0E0),
      end: AppDesignSystem.primaryColor,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: AppDesignSystem.animationCurve,
    ));
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    _animationController.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    if (mounted) {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });

      if (_isFocused) {
        _animationController.forward();
      } else {
        _animationController.reverse();
        _validateInput();
      }
    }
  }

  void _validateInput() {
    if (widget.validator != null) {
      final error = widget.validator!(widget.controller?.text);
      setState(() {
        _errorText = error;
      });
    }
  }

  void _onChanged(String value) {
    if (widget.onChanged != null) {
      widget.onChanged!(value);
    }
    
    // Clear error when user starts typing
    if (_errorText != null) {
      setState(() {
        _errorText = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final hasError = _errorText != null;
    
    return Container(
      margin: widget.margin ?? const EdgeInsets.symmetric(
        vertical: AppDesignSystem.spacingSmall,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Label
          if (widget.label != null && !widget.animateLabel)
            Padding(
              padding: const EdgeInsets.only(bottom: AppDesignSystem.spacingSmall),
              child: Text(
                widget.label!,
                style: AppDesignSystem.bodyMedium.copyWith(
                  fontWeight: AppDesignSystem.fontWeightMedium,
                ),
              ),
            ),
          
          // Text Field
          AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppDesignSystem.radiusMedium),
                  boxShadow: _isFocused
                      ? [
                          BoxShadow(
                            color: AppDesignSystem.primaryColor.withValues(alpha: 0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ]
                      : null,
                ),
                child: TextFormField(
                  controller: widget.controller,
                  focusNode: _focusNode,
                  validator: widget.validator,
                  onChanged: _onChanged,
                  onFieldSubmitted: widget.onSubmitted,
                  keyboardType: widget.keyboardType,
                  inputFormatters: widget.inputFormatters,
                  obscureText: widget.obscureText,
                  enabled: widget.enabled,
                  readOnly: widget.readOnly,
                  maxLines: widget.maxLines,
                  maxLength: widget.maxLength,
                  style: AppDesignSystem.bodyLarge,
                  decoration: InputDecoration(
                    labelText: widget.animateLabel ? widget.label : null,
                    hintText: widget.hint,
                    helperText: widget.helperText,
                    errorText: _errorText,
                    prefixIcon: widget.prefixIcon != null
                        ? Icon(
                            widget.prefixIcon,
                            color: _isFocused
                                ? AppDesignSystem.primaryColor
                                : AppDesignSystem.textHint,
                          )
                        : null,
                    suffixIcon: widget.suffixIcon != null
                        ? GestureDetector(
                            onTap: widget.onSuffixIconTap,
                            child: Icon(
                              widget.suffixIcon,
                              color: _isFocused
                                  ? AppDesignSystem.primaryColor
                                  : AppDesignSystem.textHint,
                            ),
                          )
                        : null,
                    filled: true,
                    fillColor: widget.enabled
                        ? AppDesignSystem.backgroundColor
                        : AppDesignSystem.backgroundColor.withValues(alpha: 0.5),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppDesignSystem.radiusMedium),
                      borderSide: BorderSide.none,
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppDesignSystem.radiusMedium),
                      borderSide: BorderSide(
                        color: hasError
                            ? AppDesignSystem.errorColor
                            : const Color(0xFFE0E0E0),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppDesignSystem.radiusMedium),
                      borderSide: BorderSide(
                        color: hasError
                            ? AppDesignSystem.errorColor
                            : _borderColorAnimation.value ?? AppDesignSystem.primaryColor,
                        width: 2,
                      ),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppDesignSystem.radiusMedium),
                      borderSide: const BorderSide(color: AppDesignSystem.errorColor),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppDesignSystem.radiusMedium),
                      borderSide: const BorderSide(
                        color: AppDesignSystem.errorColor,
                        width: 2,
                      ),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: AppDesignSystem.spacingMedium,
                      vertical: AppDesignSystem.spacingMedium,
                    ),
                    counterText: widget.showCharacterCount ? null : '',
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
