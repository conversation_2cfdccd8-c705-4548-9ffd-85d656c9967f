import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hue/core/theme/app_design_system.dart';
import 'package:hue/core/widgets/enhanced_button.dart';
import 'package:hue/core/widgets/enhanced_card.dart';
import 'package:hue/Pages/auth/enhanced_login.dart';
import 'package:hue/Pages/home/<USER>';
import 'package:hue/core/utils/assets.dart';
import 'dart:math' as math;

class EnhancedWelcomePage extends StatefulWidget {
  const EnhancedWelcomePage({super.key});

  @override
  State<EnhancedWelcomePage> createState() => _EnhancedWelcomePageState();
}

class _EnhancedWelcomePageState extends State<EnhancedWelcomePage>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _particleController;
  late AnimationController _contentController;
  
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _logoRotationAnimation;
  late Animation<Offset> _titleSlideAnimation;
  late Animation<double> _titleOpacityAnimation;
  late Animation<Offset> _subtitleSlideAnimation;
  late Animation<double> _subtitleOpacityAnimation;
  late Animation<Offset> _buttonsSlideAnimation;
  late Animation<double> _buttonsOpacityAnimation;

  bool _isDarkMode = false;
  final List<Particle> _particles = [];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _generateParticles();
    _startAnimations();
  }

  void _initializeAnimations() {
    // Logo animations
    _logoController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    
    _logoScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));
    
    _logoRotationAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.easeInOut,
    ));

    // Particle animation
    _particleController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..repeat();

    // Content animations
    _contentController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // Title animations
    _titleSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _contentController,
      curve: const Interval(0.2, 0.5, curve: Curves.easeOut),
    ));

    _titleOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _contentController,
      curve: const Interval(0.2, 0.5, curve: Curves.easeOut),
    ));

    // Subtitle animations
    _subtitleSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _contentController,
      curve: const Interval(0.4, 0.7, curve: Curves.easeOut),
    ));

    _subtitleOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _contentController,
      curve: const Interval(0.4, 0.7, curve: Curves.easeOut),
    ));

    // Buttons animations
    _buttonsSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _contentController,
      curve: const Interval(0.6, 1.0, curve: Curves.easeOut),
    ));

    _buttonsOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _contentController,
      curve: const Interval(0.6, 1.0, curve: Curves.easeOut),
    ));
  }

  void _generateParticles() {
    final random = math.Random();
    for (int i = 0; i < 20; i++) {
      _particles.add(Particle(
        x: random.nextDouble(),
        y: random.nextDouble(),
        size: random.nextDouble() * 4 + 2,
        speed: random.nextDouble() * 0.02 + 0.01,
        opacity: random.nextDouble() * 0.5 + 0.1,
      ));
    }
  }

  void _startAnimations() {
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _logoController.forward();
      }
    });

    Future.delayed(const Duration(milliseconds: 1000), () {
      if (mounted) {
        _contentController.forward();
      }
    });
  }

  @override
  void dispose() {
    _logoController.dispose();
    _particleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  void _toggleTheme() {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    
    return Scaffold(
      body: AnimatedContainer(
        duration: AppDesignSystem.animationDuration,
        decoration: BoxDecoration(
          gradient: _isDarkMode
              ? const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF1A1A2E),
                    Color(0xFF16213E),
                    Color(0xFF0F3460),
                  ],
                )
              : AppDesignSystem.backgroundGradient,
        ),
        child: Stack(
          children: [
            // Animated particles background
            _buildParticlesBackground(size),
            
            // Main content
            SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDesignSystem.spacingLarge,
                ),
                child: Column(
                  children: [
                    // Header with theme toggle
                    _buildHeader(),
                    
                    // Logo section
                    Expanded(
                      flex: 3,
                      child: _buildLogoSection(),
                    ),
                    
                    // Title and subtitle
                    Expanded(
                      flex: 2,
                      child: _buildTitleSection(),
                    ),
                    
                    // Action buttons
                    Expanded(
                      flex: 2,
                      child: _buildActionButtons(),
                    ),
                    
                    const SizedBox(height: AppDesignSystem.spacingLarge),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildParticlesBackground(Size size) {
    return AnimatedBuilder(
      animation: _particleController,
      builder: (context, child) {
        return Stack(
          children: _particles.map((particle) {
            final animatedY = (particle.y + 
                (_particleController.value * particle.speed)) % 1.0;
            
            return Positioned(
              left: particle.x * size.width,
              top: animatedY * size.height,
              child: Container(
                width: particle.size,
                height: particle.size,
                decoration: BoxDecoration(
                  color: (_isDarkMode ? Colors.white : AppDesignSystem.primaryColor)
                      .withValues(alpha: particle.opacity),
                  shape: BoxShape.circle,
                ),
              ),
            );
          }).toList(),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        EnhancedCard(
          padding: const EdgeInsets.all(AppDesignSystem.spacingSmall),
          backgroundColor: Colors.white.withValues(alpha: 0.1),
          onTap: _toggleTheme,
          child: Icon(
            _isDarkMode ? Icons.light_mode : Icons.dark_mode,
            color: _isDarkMode ? Colors.white : AppDesignSystem.textPrimary,
            size: 24,
          ),
        ),
      ],
    );
  }

  Widget _buildLogoSection() {
    return Center(
      child: AnimatedBuilder(
        animation: _logoController,
        builder: (context, child) {
          return Transform.scale(
            scale: _logoScaleAnimation.value,
            child: Transform.rotate(
              angle: _logoRotationAnimation.value * 0.1,
              child: Container(
                padding: const EdgeInsets.all(AppDesignSystem.spacingLarge),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      AppDesignSystem.primaryColor.withValues(alpha: 0.2),
                      Colors.transparent,
                    ],
                  ),
                ),
                child: Image.asset(
                  _isDarkMode ? Assets.imagesLogo : Assets.imagesLogoBlue,
                  width: 200,
                  height: 200,
                  fit: BoxFit.contain,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTitleSection() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Main title
        SlideTransition(
          position: _titleSlideAnimation,
          child: FadeTransition(
            opacity: _titleOpacityAnimation,
            child: ShaderMask(
              shaderCallback: (bounds) => const LinearGradient(
                colors: [
                  AppDesignSystem.primaryColor,
                  AppDesignSystem.secondaryColor,
                ],
              ).createShader(bounds),
              child: Text(
                'مرحباً بك في HUE',
                style: AppDesignSystem.headingLarge.copyWith(
                  fontSize: 32,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
        
        const SizedBox(height: AppDesignSystem.spacingMedium),
        
        // Subtitle
        SlideTransition(
          position: _subtitleSlideAnimation,
          child: FadeTransition(
            opacity: _subtitleOpacityAnimation,
            child: Text(
              'رفيقك التعليمي الذكي\nاكتشف عالماً جديداً من التعلم والمعرفة',
              style: AppDesignSystem.bodyLarge.copyWith(
                color: _isDarkMode 
                    ? Colors.white.withValues(alpha: 0.8)
                    : AppDesignSystem.textSecondary,
                height: 1.6,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return SlideTransition(
      position: _buttonsSlideAnimation,
      child: FadeTransition(
        opacity: _buttonsOpacityAnimation,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            EnhancedButton(
              text: 'استكشف التطبيق',
              type: ButtonType.gradient,
              size: ButtonSize.large,
              icon: Icons.explore,
              isFullWidth: true,
              onPressed: () => Get.to(() => const HomePage()),
            ),
            
            const SizedBox(height: AppDesignSystem.spacingMedium),
            
            EnhancedButton(
              text: 'تسجيل الدخول',
              type: ButtonType.outline,
              size: ButtonSize.large,
              icon: Icons.login,
              isFullWidth: true,
              customColor: _isDarkMode ? Colors.white : AppDesignSystem.primaryColor,
              onPressed: () => Get.to(() => const EnhancedLogin()),
            ),
            
            const SizedBox(height: AppDesignSystem.spacingMedium),
            
            EnhancedButton(
              text: 'إنشاء حساب جديد',
              type: ButtonType.text,
              size: ButtonSize.medium,
              icon: Icons.person_add,
              customColor: _isDarkMode ? Colors.white : AppDesignSystem.primaryColor,
              onPressed: () {
                Get.snackbar(
                  'قريباً',
                  'ميزة إنشاء حساب جديد ستكون متاحة قريباً',
                  backgroundColor: AppDesignSystem.infoColor,
                  colorText: Colors.white,
                  duration: const Duration(seconds: 3),
                  snackPosition: SnackPosition.BOTTOM,
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

class Particle {
  final double x;
  final double y;
  final double size;
  final double speed;
  final double opacity;

  Particle({
    required this.x,
    required this.y,
    required this.size,
    required this.speed,
    required this.opacity,
  });
}