import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hue/core/theme/app_design_system.dart';
import 'package:hue/quick_test_enhanced.dart';

/// تشغيل اختبار سريع للمكونات المحسنة
void main() {
  runApp(const QuickTestApp());
}

class QuickTestApp extends StatelessWidget {
  const QuickTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'اختبار سريع - المكونات المحسنة',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      home: const QuickTestEnhanced(),
      locale: const Locale('ar', 'SA'),
      fallbackLocale: const Locale('en', 'US'),
    );
  }
}
