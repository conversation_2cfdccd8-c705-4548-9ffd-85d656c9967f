import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:hue/core/widgets/app_background.dart';
import 'package:hue/core/services/navigation_service.dart';
import 'package:hue/core/services/error_handler.dart';
import 'package:hue/core/services/sms_service.dart';
import 'package:hue/core/utils/constants.dart';
import 'package:hue/Pages/home/<USER>';

class MultiStepSignUpPage extends StatefulWidget {
  const MultiStepSignUpPage({super.key});

  @override
  State<MultiStepSignUpPage> createState() => _MultiStepSignUpPageState();
}

class _MultiStepSignUpPageState extends State<MultiStepSignUpPage> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  final int _totalSteps = 4;

  // Controllers for all form fields
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _ageController = TextEditingController();
  
  // Form keys for validation
  final _step1FormKey = GlobalKey<FormState>();
  final _step2FormKey = GlobalKey<FormState>();
  final _step3FormKey = GlobalKey<FormState>();
  final _step4FormKey = GlobalKey<FormState>();

  // State variables
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _isLoading = false;
  bool _isPhoneVerified = false;
  String? _selectedCollege;
  String? _selectedMajor;

  // College and major options
  final List<String> _colleges = [
    'كلية الهندسة',
    'كلية الطب',
    'كلية الصيدلة',
    'كلية طب الأسنان',
    'كلية إدارة الأعمال',
    'كلية الفنون الجميلة',
    'كلية اللغات',
  ];

  final Map<String, List<String>> _majors = {
    'كلية الهندسة': ['هندسة مدنية', 'هندسة كهربائية', 'هندسة حاسوب', 'هندسة ميكانيكية'],
    'كلية الطب': ['طب عام', 'جراحة', 'باطنة', 'أطفال'],
    'كلية الصيدلة': ['صيدلة إكلينيكية', 'صيدلة صناعية', 'علم الأدوية'],
    'كلية طب الأسنان': ['طب أسنان عام', 'تقويم أسنان', 'جراحة فم'],
    'كلية إدارة الأعمال': ['إدارة', 'محاسبة', 'تسويق', 'موارد بشرية'],
    'كلية الفنون الجميلة': ['رسم', 'نحت', 'تصميم جرافيك'],
    'كلية اللغات': ['إنجليزية', 'فرنسية', 'ألمانية', 'عربية'],
  };

  @override
  void dispose() {
    _pageController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _ageController.dispose();
    super.dispose();
  }

  // Navigation methods
  void _nextStep() {
    if (_validateCurrentStep()) {
      if (_currentStep < _totalSteps - 1) {
        setState(() {
          _currentStep++;
        });
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      } else {
        _submitForm();
      }
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  // Validation for current step
  bool _validateCurrentStep() {
    switch (_currentStep) {
      case 0:
        return _step1FormKey.currentState?.validate() ?? false;
      case 1:
        final isValid = _step2FormKey.currentState?.validate() ?? false;
        if (isValid && !_isPhoneVerified) {
          // Show phone verification dialog
          _verifyPhoneNumber();
          return false;
        }
        return isValid && _isPhoneVerified;
      case 2:
        return _step3FormKey.currentState?.validate() ?? false;
      case 3:
        return _step4FormKey.currentState?.validate() ?? false;
      default:
        return false;
    }
  }

  // Verify phone number
  Future<void> _verifyPhoneNumber() async {
    final phoneNumber = _phoneController.text.trim().replaceAll(RegExp(r'[^0-9]'), '');

    if (phoneNumber.isEmpty) {
      NavigationService.showErrorSnackbar('يرجى إدخال رقم الهاتف أولاً');
      return;
    }

    // Show verification method selection
    await SmsService.showVerificationMethodDialog(phoneNumber);

    // Show verification code input dialog
    final isVerified = await SmsService.showVerificationCodeDialog(phoneNumber);

    if (isVerified) {
      setState(() {
        _isPhoneVerified = true;
      });

      // Automatically proceed to next step after verification
      _nextStep();
    }
  }

  // Submit the complete form
  Future<void> _submitForm() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Create account with Supabase
      final response = await Supabase.instance.client.auth.signUp(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        data: {
          'first_name': _firstNameController.text.trim(),
          'last_name': _lastNameController.text.trim(),
          'phone': _phoneController.text.trim().replaceAll(RegExp(r'[^0-9]'), ''),
          'age': int.tryParse(_ageController.text) ?? 0,
          'college': _selectedCollege,
          'major': _selectedMajor,
          'full_name': '${_firstNameController.text.trim()} ${_lastNameController.text.trim()}',
        },
      );

      if (response.user != null) {
        NavigationService.showSuccessSnackbar('تم إنشاء الحساب بنجاح!');
        await Future.delayed(const Duration(milliseconds: 500));
        if (mounted) {
          NavigationService.navigateAndRemoveUntil(const HomePage());
        }
      } else {
        NavigationService.showErrorSnackbar('فشل في إنشاء الحساب');
      }
    } catch (e) {
      final errorMessage = ErrorHandler.handleSupabaseError(e);
      NavigationService.showErrorSnackbar(errorMessage);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('إنشاء حساب جديد'),
        centerTitle: true,
        elevation: 0,
      ),
      body: AppBackground(
        child: Column(
          children: [
            // Progress indicator
            _buildProgressIndicator(),
            
            // Form content
            Expanded(
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  _buildStep1(), // Personal Information
                  _buildStep2(), // Contact Information
                  _buildStep3(), // Password & Security
                  _buildStep4(), // Additional Information
                ],
              ),
            ),
            
            // Navigation buttons
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  // Progress indicator widget
  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: List.generate(_totalSteps, (index) {
          return Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 2),
              height: 4,
              decoration: BoxDecoration(
                color: index <= _currentStep
                    ? Theme.of(context).primaryColor
                    : Colors.grey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          );
        }),
      ),
    );
  }

  // Navigation buttons
  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _isLoading ? null : _previousStep,
                child: const Text('السابق'),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _isLoading ? null : _nextStep,
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(_currentStep == _totalSteps - 1 ? 'إنشاء الحساب' : 'التالي'),
            ),
          ),
        ],
      ),
    );
  }

  // Step 1: Personal Information
  Widget _buildStep1() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Form(
        key: _step1FormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),

            // Step title
            Text(
              'المعلومات الشخصية',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),

            const SizedBox(height: 8),

            Text(
              'أدخل اسمك الأول والأخير',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),

            const SizedBox(height: 40),

            // First Name
            TextFormField(
              controller: _firstNameController,
              textInputAction: TextInputAction.next,
              decoration: const InputDecoration(
                labelText: 'الاسم الأول',
                hintText: 'أدخل اسمك الأول',
                prefixIcon: Icon(Icons.person_outline),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال الاسم الأول';
                }
                if (value.trim().length < 2) {
                  return 'الاسم يجب أن يكون أكثر من حرف واحد';
                }
                // Check if name contains numbers
                if (value.contains(RegExp(r'[0-9]'))) {
                  return 'الاسم لا يجب أن يحتوي على أرقام';
                }
                return null;
              },
            ),

            const SizedBox(height: 20),

            // Last Name
            TextFormField(
              controller: _lastNameController,
              textInputAction: TextInputAction.done,
              decoration: const InputDecoration(
                labelText: 'الاسم الأخير',
                hintText: 'أدخل اسمك الأخير',
                prefixIcon: Icon(Icons.person_outline),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال الاسم الأخير';
                }
                if (value.trim().length < 2) {
                  return 'الاسم يجب أن يكون أكثر من حرف واحد';
                }
                // Check if name contains numbers
                if (value.contains(RegExp(r'[0-9]'))) {
                  return 'الاسم لا يجب أن يحتوي على أرقام';
                }
                return null;
              },
            ),

            const SizedBox(height: 40),

            // Welcome message
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'تأكد من إدخال اسمك الحقيقي كما هو مكتوب في الوثائق الرسمية',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Step 2: Contact Information
  Widget _buildStep2() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Form(
        key: _step2FormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),

            // Step title
            Text(
              'معلومات الاتصال',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),

            const SizedBox(height: 8),

            Text(
              'أدخل بريدك الإلكتروني ورقم هاتفك',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),

            const SizedBox(height: 40),

            // Email
            TextFormField(
              controller: _emailController,
              keyboardType: TextInputType.emailAddress,
              textInputAction: TextInputAction.next,
              decoration: const InputDecoration(
                labelText: 'البريد الإلكتروني',
                hintText: '<EMAIL>',
                prefixIcon: Icon(Icons.email_outlined),
                helperText: 'استخدم بريدك الجامعي',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال البريد الإلكتروني';
                }

                value = value.trim();

                if (!GetUtils.isEmail(value)) {
                  return 'يرجى إدخال بريد إلكتروني صحيح';
                }

                if (!value.endsWith(Constants.emailDomain)) {
                  return Constants.invalidEmailDomain;
                }

                return null;
              },
            ),

            const SizedBox(height: 20),

            // Phone Number
            TextFormField(
              controller: _phoneController,
              keyboardType: TextInputType.phone,
              textInputAction: TextInputAction.done,
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف',
                hintText: '01xxxxxxxxx',
                prefixIcon: Icon(Icons.phone_outlined),
                helperText: 'رقم الهاتف المحمول',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال رقم الهاتف';
                }

                value = value.trim();

                // Remove any non-digit characters
                value = value.replaceAll(RegExp(r'[^0-9]'), '');

                if (value.length != 11) {
                  return 'رقم الهاتف يجب أن يكون 11 رقم بالضبط';
                }

                if (!value.startsWith('01')) {
                  return 'رقم الهاتف يجب أن يبدأ بـ 01';
                }

                // Check if it's a valid Egyptian mobile number
                final validPrefixes = ['010', '011', '012', '015'];
                final prefix = value.substring(0, 3);
                if (!validPrefixes.contains(prefix)) {
                  return 'رقم الهاتف غير صحيح (يجب أن يبدأ بـ 010, 011, 012, أو 015)';
                }

                return null;
              },
            ),

            const SizedBox(height: 20),

            // Phone verification button
            if (!_isPhoneVerified)
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: _verifyPhoneNumber,
                  icon: const Icon(Icons.verified_user),
                  label: const Text('تحقق من رقم الهاتف'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),

            // Verification status
            if (_isPhoneVerified)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.check_circle, color: Colors.green),
                    const SizedBox(width: 8),
                    Text(
                      'تم التحقق من رقم الهاتف بنجاح',
                      style: TextStyle(
                        color: Colors.green[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 40),

            // Info message
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.blue.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.security_outlined,
                    color: Colors.blue,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'سيتم استخدام هذه المعلومات للتواصل معك وإرسال التنبيهات المهمة',
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Step 3: Password & Security
  Widget _buildStep3() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Form(
        key: _step3FormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),

            // Step title
            Text(
              'كلمة المرور والأمان',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),

            const SizedBox(height: 8),

            Text(
              'اختر كلمة مرور قوية لحماية حسابك',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),

            const SizedBox(height: 40),

            // Password
            TextFormField(
              controller: _passwordController,
              obscureText: _obscurePassword,
              textInputAction: TextInputAction.next,
              decoration: InputDecoration(
                labelText: 'كلمة المرور',
                hintText: 'أدخل كلمة مرور قوية',
                prefixIcon: const Icon(Icons.lock_outlined),
                helperText: 'على الأقل 8 أحرف مع أرقام وحروف',
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscurePassword
                        ? Icons.visibility_outlined
                        : Icons.visibility_off_outlined,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  },
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال كلمة المرور';
                }

                if (value.length < 8) {
                  return Constants.invalidPassword;
                }

                // Check password strength
                final hasLetters = value.contains(RegExp(r'[a-zA-Z]'));
                final hasNumbers = value.contains(RegExp(r'[0-9]'));

                if (!hasLetters || !hasNumbers) {
                  return 'كلمة المرور يجب أن تحتوي على أحرف وأرقام';
                }

                return null;
              },
            ),

            const SizedBox(height: 20),

            // Confirm Password
            TextFormField(
              controller: _confirmPasswordController,
              obscureText: _obscureConfirmPassword,
              textInputAction: TextInputAction.done,
              decoration: InputDecoration(
                labelText: 'تأكيد كلمة المرور',
                hintText: 'أعد إدخال كلمة المرور',
                prefixIcon: const Icon(Icons.lock_outlined),
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscureConfirmPassword
                        ? Icons.visibility_outlined
                        : Icons.visibility_off_outlined,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscureConfirmPassword = !_obscureConfirmPassword;
                    });
                  },
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى تأكيد كلمة المرور';
                }

                if (value != _passwordController.text) {
                  return Constants.passwordMismatch;
                }

                return null;
              },
            ),

            const SizedBox(height: 40),

            // Security tips
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.green.withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.security,
                        color: Colors.green,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'نصائح لكلمة مرور قوية:',
                        style: TextStyle(
                          color: Colors.green[700],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• استخدم على الأقل 8 أحرف\n• امزج بين الأحرف والأرقام\n• تجنب المعلومات الشخصية\n• لا تشارك كلمة المرور مع أحد',
                    style: TextStyle(
                      color: Colors.green[700],
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Step 4: Additional Information
  Widget _buildStep4() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Form(
        key: _step4FormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),

            // Step title
            Text(
              'معلومات إضافية',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),

            const SizedBox(height: 8),

            Text(
              'أكمل معلوماتك الأكاديمية',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),

            const SizedBox(height: 40),

            // Age
            TextFormField(
              controller: _ageController,
              keyboardType: TextInputType.number,
              textInputAction: TextInputAction.next,
              decoration: const InputDecoration(
                labelText: 'العمر',
                hintText: 'أدخل عمرك',
                prefixIcon: Icon(Icons.cake_outlined),
                helperText: 'يجب أن تكون 18 سنة أو أكثر',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال العمر';
                }

                final age = int.tryParse(value.trim());
                if (age == null) {
                  return 'يرجى إدخال عمر صحيح';
                }

                if (age < 18) {
                  return 'يجب أن تكون 18 سنة أو أكثر';
                }

                if (age > 100) {
                  return 'يرجى إدخال عمر صحيح';
                }

                return null;
              },
            ),

            const SizedBox(height: 20),

            // College Dropdown
            DropdownButtonFormField<String>(
              value: _selectedCollege,
              decoration: const InputDecoration(
                labelText: 'الكلية',
                prefixIcon: Icon(Icons.school_outlined),
                helperText: 'اختر كليتك',
              ),
              items: _colleges.map((college) {
                return DropdownMenuItem(
                  value: college,
                  child: Text(college),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedCollege = value;
                  _selectedMajor = null; // Reset major when college changes
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى اختيار الكلية';
                }
                return null;
              },
            ),

            const SizedBox(height: 20),

            // Major Dropdown
            DropdownButtonFormField<String>(
              value: _selectedMajor,
              decoration: const InputDecoration(
                labelText: 'التخصص',
                prefixIcon: Icon(Icons.book_outlined),
                helperText: 'اختر تخصصك',
              ),
              items: _selectedCollege != null
                  ? _majors[_selectedCollege]?.map((major) {
                      return DropdownMenuItem(
                        value: major,
                        child: Text(major),
                      );
                    }).toList()
                  : [],
              onChanged: _selectedCollege != null
                  ? (value) {
                      setState(() {
                        _selectedMajor = value;
                      });
                    }
                  : null,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى اختيار التخصص';
                }
                return null;
              },
            ),

            const SizedBox(height: 40),

            // Final message
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.celebration_outlined,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'أنت على وشك الانتهاء! اضغط "إنشاء الحساب" لإكمال التسجيل',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
