import 'package:flutter_test/flutter_test.dart';
import 'package:hue/core/services/sms_service.dart';

void main() {
  group('SMS Service Tests', () {
    
    test('Generate verification code should return 6-digit string', () {
      final code = SmsService.generateVerificationCode();
      
      // Check if code is 6 digits
      expect(code.length, 6);
      
      // Check if code contains only numbers
      expect(int.tryParse(code), isNotNull);
      
      // Check if code is within valid range
      final codeInt = int.parse(code);
      expect(codeInt, greaterThanOrEqualTo(100000));
      expect(codeInt, lessThanOrEqualTo(999999));
    });
    
    test('Generate multiple codes should be different', () {
      final code1 = SmsService.generateVerificationCode();
      final code2 = SmsService.generateVerificationCode();
      final code3 = SmsService.generateVerificationCode();
      
      // Codes should be different (very high probability)
      expect(code1 == code2 && code2 == code3, false);
    });
    
    test('Phone number formatting for WhatsApp should work correctly', () {
      // Test Egyptian phone numbers
      const testCases = [
        {'input': '01012345678', 'expected': '21012345678'},
        {'input': '01112345678', 'expected': '21112345678'},
        {'input': '01212345678', 'expected': '21212345678'},
        {'input': '01512345678', 'expected': '21512345678'},
      ];
      
      for (final testCase in testCases) {
        final input = testCase['input']!;
        final expected = testCase['expected']!;
        
        // Simulate the formatting logic
        String formatted = input;
        if (formatted.startsWith('0')) {
          formatted = '2${formatted.substring(1)}';
        }
        
        expect(formatted, expected);
      }
    });
    
    test('Verification code validation should work correctly', () {
      // Test valid codes
      const validCodes = ['123456', '000000', '999999', '100000'];
      for (final code in validCodes) {
        expect(code.length, 6);
        expect(int.tryParse(code), isNotNull);
      }
      
      // Test invalid codes
      const invalidCodes = ['12345', '1234567', 'abcdef', '12345a', ''];
      for (final code in invalidCodes) {
        final isValid = code.length == 6 && int.tryParse(code) != null;
        expect(isValid, false);
      }
    });
    
    test('Egyptian phone number validation should work correctly', () {
      // Valid Egyptian mobile numbers
      const validNumbers = [
        '01012345678',
        '01112345678', 
        '01212345678',
        '01512345678',
      ];
      
      for (final number in validNumbers) {
        expect(number.length, 11);
        expect(number.startsWith('01'), true);
        
        final validPrefixes = ['010', '011', '012', '015'];
        final prefix = number.substring(0, 3);
        expect(validPrefixes.contains(prefix), true);
      }
      
      // Invalid numbers
      const invalidNumbers = [
        '0101234567',   // Too short
        '010123456789', // Too long
        '02012345678',  // Wrong prefix
        '01312345678',  // Invalid prefix
        '1012345678',   // Missing leading 0
      ];
      
      for (final number in invalidNumbers) {
        final isValid = number.length == 11 && 
                       number.startsWith('01') &&
                       ['010', '011', '012', '015'].contains(number.substring(0, 3));
        expect(isValid, false);
      }
    });
    
    test('WhatsApp URL generation should be correct', () {
      const phoneNumber = '01012345678';
      const message = 'مرحباً! رمز التحقق: 123456';
      
      // Simulate URL generation
      String formattedNumber = phoneNumber;
      if (formattedNumber.startsWith('0')) {
        formattedNumber = '2${formattedNumber.substring(1)}';
      }
      
      final expectedUrl = 'https://wa.me/$formattedNumber?text=${Uri.encodeComponent(message)}';
      
      expect(formattedNumber, '21012345678');
      expect(expectedUrl.contains('wa.me'), true);
      expect(expectedUrl.contains('21012345678'), true);
      expect(expectedUrl.contains('text='), true);
    });
    
    test('Verification message format should be correct', () {
      const code = '123456';
      const message = '''
مرحباً بك في تطبيق HUE! 🎓

رمز التحقق الخاص بك هو: $code

هذا الرمز صالح لمدة 5 دقائق فقط.
لا تشارك هذا الرمز مع أي شخص آخر.

فريق HUE
''';
      
      expect(message.contains(code), true);
      expect(message.contains('HUE'), true);
      expect(message.contains('5 دقائق'), true);
      expect(message.contains('لا تشارك'), true);
    });
    
    test('WhatsApp message format should be correct', () {
      const code = '123456';
      const message = '''
🎓 مرحباً بك في تطبيق HUE!

رمز التحقق الخاص بك هو: *$code*

⏰ هذا الرمز صالح لمدة 5 دقائق فقط
🔒 لا تشارك هذا الرمز مع أي شخص آخر

شكراً لك!
فريق HUE 💙
''';
      
      expect(message.contains('*$code*'), true);
      expect(message.contains('🎓'), true);
      expect(message.contains('⏰'), true);
      expect(message.contains('🔒'), true);
      expect(message.contains('💙'), true);
    });
    
    test('Code expiry time calculation should be correct', () {
      final now = DateTime.now();
      final expiryTime = now.add(const Duration(minutes: 5));
      
      expect(expiryTime.isAfter(now), true);
      expect(expiryTime.difference(now).inMinutes, 5);
      expect(expiryTime.difference(now).inSeconds, 300);
    });
    
    test('Phone number cleaning should work correctly', () {
      const testCases = [
        {'input': '010-123-45678', 'expected': '01012345678'},
        {'input': '010 123 45678', 'expected': '01012345678'},
        {'input': '(010) 123-45678', 'expected': '01012345678'},
        {'input': '+20 10 123 45678', 'expected': '201012345678'},
        {'input': '010.123.45678', 'expected': '01012345678'},
      ];
      
      for (final testCase in testCases) {
        final input = testCase['input']!;
        final expected = testCase['expected']!;
        
        // Simulate phone number cleaning
        final cleaned = input.replaceAll(RegExp(r'[^0-9]'), '');
        
        expect(cleaned, expected);
      }
    });
  });
}
