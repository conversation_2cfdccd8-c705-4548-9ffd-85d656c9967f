import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:hue/core/theme/app_design_system.dart';
import 'package:hue/core/widgets/enhanced_button.dart';
import 'package:hue/core/widgets/enhanced_text_field.dart';
import 'package:hue/core/widgets/enhanced_card.dart';
import 'package:hue/core/services/navigation_service.dart';
import 'package:hue/core/utils/assets.dart';
import 'package:hue/Pages/home/<USER>';

class EnhancedLogin extends StatefulWidget {
  const EnhancedLogin({super.key});

  @override
  State<EnhancedLogin> createState() => _EnhancedLoginState();
}

class _EnhancedLoginState extends State<EnhancedLogin>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  
  late AnimationController _animationController;
  late AnimationController _shakeController;
  
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shakeAnimation;
  
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _rememberMe = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 0.8, curve: Curves.easeOut),
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.4, 1.0, curve: Curves.elasticOut),
    ));

    _shakeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _shakeController,
      curve: Curves.elasticIn,
    ));
  }

  void _startAnimations() {
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _shakeController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _shakeForm() {
    _shakeController.reset();
    _shakeController.forward();
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال البريد الإلكتروني';
    }
    if (!GetUtils.isEmail(value)) {
      return 'يرجى إدخال بريد إلكتروني صحيح';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'يرجى إدخال كلمة المرور';
    }
    if (value.length < 6) {
      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    return null;
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) {
      _shakeForm();
      return;
    }

    setState(() => _isLoading = true);

    try {
      final response = await Supabase.instance.client.auth.signInWithPassword(
        email: _emailController.text.trim(),
        password: _passwordController.text,
      );

      if (response.user != null) {
        NavigationService.showSuccessSnackbar('تم تسجيل الدخول بنجاح');
        Get.offAll(() => const HomePage());
      }
    } catch (e) {
      _shakeForm();
      if (mounted) {
        Get.snackbar(
          'خطأ',
          'فشل في تسجيل الدخول. يرجى المحاولة مرة أخرى.',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppDesignSystem.backgroundGradient,
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppDesignSystem.spacingLarge),
            child: SizedBox(
              height: MediaQuery.of(context).size.height - 
                     MediaQuery.of(context).padding.top - 
                     AppDesignSystem.spacingLarge * 2,
              child: Column(
                children: [
                  // Header
                  _buildHeader(),
                  
                  // Login Form
                  Expanded(
                    child: _buildLoginForm(),
                  ),
                  
                  // Footer
                  _buildFooter(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Column(
        children: [
          const SizedBox(height: AppDesignSystem.spacingXLarge),
          
          // Logo
          ScaleTransition(
            scale: _scaleAnimation,
            child: Container(
              padding: const EdgeInsets.all(AppDesignSystem.spacingLarge),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: AppDesignSystem.primaryColor.withValues(alpha: 0.2),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Image.asset(
                Assets.imagesLogoBlue,
                width: 80,
                height: 80,
              ),
            ),
          ),
          
          const SizedBox(height: AppDesignSystem.spacingLarge),
          
          // Title
          Text(
            'مرحباً بعودتك',
            style: AppDesignSystem.headingLarge.copyWith(
              color: AppDesignSystem.primaryColor,
            ),
          ),
          
          const SizedBox(height: AppDesignSystem.spacingSmall),
          
          Text(
            'سجل دخولك للمتابعة',
            style: AppDesignSystem.bodyLarge.copyWith(
              color: AppDesignSystem.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoginForm() {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Center(
          child: AnimatedBuilder(
            animation: _shakeAnimation,
            builder: (context, child) {
              final offset = _shakeAnimation.value * 10 * 
                           (1 - _shakeAnimation.value);
              return Transform.translate(
                offset: Offset(offset, 0),
                child: EnhancedCard(
                  margin: const EdgeInsets.symmetric(
                    vertical: AppDesignSystem.spacingLarge,
                  ),
                  padding: const EdgeInsets.all(AppDesignSystem.spacingXLarge),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Email field
                        EnhancedTextField(
                          controller: _emailController,
                          label: 'البريد الإلكتروني',
                          hint: 'أدخل بريدك الإلكتروني',
                          prefixIcon: Icons.email_outlined,
                          keyboardType: TextInputType.emailAddress,
                          validator: _validateEmail,
                        ),
                        
                        const SizedBox(height: AppDesignSystem.spacingMedium),
                        
                        // Password field
                        EnhancedTextField(
                          controller: _passwordController,
                          label: 'كلمة المرور',
                          hint: 'أدخل كلمة المرور',
                          prefixIcon: Icons.lock_outlined,
                          suffixIcon: _obscurePassword 
                              ? Icons.visibility_outlined
                              : Icons.visibility_off_outlined,
                          onSuffixIconTap: () {
                            setState(() {
                              _obscurePassword = !_obscurePassword;
                            });
                          },
                          obscureText: _obscurePassword,
                          validator: _validatePassword,
                        ),
                        
                        const SizedBox(height: AppDesignSystem.spacingMedium),
                        
                        // Remember me & Forgot password
                        Row(
                          children: [
                            Checkbox(
                              value: _rememberMe,
                              onChanged: (value) {
                                setState(() {
                                  _rememberMe = value ?? false;
                                });
                              },
                              activeColor: AppDesignSystem.primaryColor,
                            ),
                            const Text('تذكرني'),
                            const Spacer(),
                            TextButton(
                              onPressed: () {
                                Get.snackbar(
                                  'قريباً',
                                  'ميزة استعادة كلمة المرور ستكون متاحة قريباً',
                                  backgroundColor: AppDesignSystem.infoColor,
                                  colorText: Colors.white,
                                );
                              },
                              child: const Text('نسيت كلمة المرور؟'),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: AppDesignSystem.spacingLarge),
                        
                        // Login button
                        EnhancedButton(
                          text: 'تسجيل الدخول',
                          type: ButtonType.gradient,
                          size: ButtonSize.large,
                          icon: Icons.login,
                          isFullWidth: true,
                          isLoading: _isLoading,
                          onPressed: _isLoading ? null : _login,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildFooter() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Column(
        children: [
          const Text('ليس لديك حساب؟'),
          const SizedBox(height: AppDesignSystem.spacingSmall),
          EnhancedButton(
            text: 'إنشاء حساب جديد',
            type: ButtonType.outline,
            size: ButtonSize.medium,
            icon: Icons.person_add,
            onPressed: () {
              Get.snackbar(
                'قريباً',
                'ميزة إنشاء حساب جديد ستكون متاحة قريباً',
                backgroundColor: AppDesignSystem.infoColor,
                colorText: Colors.white,
              );
            },
          ),
        ],
      ),
    );
  }
}
